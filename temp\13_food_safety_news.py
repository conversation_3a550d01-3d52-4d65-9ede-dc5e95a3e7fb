#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Food Safety News文章抓取脚本
使用Selenium抓取https://www.foodsafetynews.com/sections/food-recalls/页面的文章数据
"""

import re
import time
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from bs4 import BeautifulSoup


class FoodSafetyNewsScraper:
    def __init__(self):
        self.base_url = "https://www.foodsafetynews.com/sections/food-recalls/"
        self.driver = None
        self.setup_driver()

    def setup_driver(self):
        # 尝试连接到现有的Chrome实例
        try:
            # 连接到已经运行的Chrome实例（需要先手动启动Chrome并开启调试端口）
            options = Options()
            options.add_experimental_option("debuggerAddress", "127.0.0.1:9222")

            self.driver = webdriver.Chrome(options=options)
            print("成功连接到现有的Chrome浏览器实例")

        except Exception as e:
            print(f"无法连接到现有Chrome实例: {e}")
            print("请先手动启动Chrome浏览器，并使用以下命令：")
            print("chrome.exe --remote-debugging-port=9222 --user-data-dir=C:/temp/chrome_debug")
            print("或者脚本将启动新的浏览器实例...")

            # 如果连接失败，启动新的Chrome实例
            options = Options()
            options.add_argument('--disable-blink-features=AutomationControlled')
            options.add_experimental_option("excludeSwitches", ["enable-automation"])
            options.add_experimental_option('useAutomationExtension', False)

            self.driver = webdriver.Chrome(options=options)

    def extract_article_data(self, article_element):
        try:
            title_element = article_element.find('h1', class_='lxb_af-template_tags-get_linked_post_title')
            if not title_element:
                return None

            title_link = title_element.find('a')
            if not title_link:
                return None

            title = title_link.get_text(strip=True)
            article_url = title_link.get('href', '')

            time_element = article_element.find('time', class_='lxb_af-template_tags-get_post_date')
            publication_time = ''
            if time_element:
                publication_time = time_element.get_text(strip=True)

            content_div = article_element.find('div', class_='lxb_af-post_content')
            content = ''
            if content_div:
                paragraphs = content_div.find_all('p')
                content_parts = []
                for p in paragraphs:
                    p_text = p.get_text(strip=True)
                    if p_text and not p_text.startswith('Continue Reading'):
                        content_parts.append(p_text)
                content = ' '.join(content_parts)

                if not content:
                    content = content_div.get_text(strip=True)
                    content = re.sub(r'Continue Reading.*$', '', content).strip()

            return {
                'title': title,
                'time': publication_time,
                'content': content,
                'url': article_url
            }

        except Exception as e:
            print(f"提取文章数据时出错: {e}")
            return None

    def scroll_to_load_more(self):
        try:
            self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
            time.sleep(3)

            try:
                load_more_button = self.driver.find_element(By.XPATH, "//a[contains(text(), 'Load More') or contains(text(), 'More')]")
                if load_more_button.is_displayed():
                    load_more_button.click()
                    print("点击了Load More按钮")
                    time.sleep(5)
                    return True
            except:
                pass

            return False

        except Exception as e:
            print(f"滚动加载更多时出错: {e}")
            return False

    def scrape_page(self, url):
        try:
            print(f"正在访问: {url}")
            self.driver.get(url)
            time.sleep(5)

            try:
                WebDriverWait(self.driver, 15).until(
                    EC.presence_of_element_located((By.TAG_NAME, "article"))
                )
            except:
                print("未找到文章元素")
                print(f"当前页面标题: {self.driver.title}")
                pass

            all_articles = []

            for i in range(3):
                soup = BeautifulSoup(self.driver.page_source, 'html.parser')
                articles = soup.find_all('article')

                current_articles = []
                for article in articles:
                    article_data = self.extract_article_data(article)
                    if article_data:
                        current_articles.append(article_data)

                print(f"当前找到 {len(current_articles)} 篇文章")

                if not self.scroll_to_load_more():
                    all_articles = current_articles
                    break

                all_articles = current_articles

            return all_articles

        except Exception as e:
            print(f"抓取页面时出错: {e}")
            return []

    def scrape_all_articles(self):
        all_articles = []

        try:
            articles = self.scrape_page(self.base_url)
            all_articles.extend(articles)

            print(f"总共抓取到 {len(articles)} 篇文章")

        except KeyboardInterrupt:
            print(f"\n用户中断，已抓取 {len(all_articles)} 篇文章")
        except Exception as e:
            print(f"抓取过程中出错: {e}")

        return all_articles

    def close(self):
        if self.driver:
            self.driver.quit()


def main():
    print("Food Safety News文章抓取脚本")
    print("注意: 如果遇到Cloudflare验证，脚本会自动等待")
    print("-" * 60)

    scraper = FoodSafetyNewsScraper()

    try:
        articles = scraper.scrape_all_articles()

        print(f"\n" + "=" * 60)
        print(f"抓取完成！总共获取到 {len(articles)} 篇文章")
        print("=" * 60)

        for i, article in enumerate(articles, 1):
            print(f"\n{i}. 标题: {article['title']}")
            print(f"   时间: {article['time']}")
            print(f"   内容: {article['content'][:200]}..." if len(article['content']) > 200 else f"   内容: {article['content']}")
            print(f"   链接: {article['url']}")
            print("-" * 60)

    finally:
        scraper.close()


if __name__ == "__main__":
    main()