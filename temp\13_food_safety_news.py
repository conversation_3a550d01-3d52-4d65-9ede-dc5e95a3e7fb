#!/usr/bin/env python3
"""
Food Safety News Scraper
Scrapes articles from https://www.foodsafetynews.com/sections/food-recalls/
Extracts: title, date, content, and article URL
"""

import time
import json
import csv
from datetime import datetime
from typing import List, Dict, Optional
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('food_safety_scraper.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class FoodSafetyNewsScraper:
    """Scraper for Food Safety News website"""

    def __init__(self, headless: bool = True, timeout: int = 10):
        """
        Initialize the scraper

        Args:
            headless: Run browser in headless mode
            timeout: Default timeout for element waits
        """
        self.base_url = "https://www.foodsafetynews.com/sections/food-recalls/"
        self.timeout = timeout
        self.driver = None
        self.wait = None
        self.headless = headless

    def setup_driver(self):
        """Setup Chrome WebDriver with options"""
        try:
            chrome_options = Options()
            if self.headless:
                chrome_options.add_argument("--headless")
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-gpu")
            chrome_options.add_argument("--window-size=1920,1080")
            chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")

            self.driver = webdriver.Chrome(options=chrome_options)
            self.wait = WebDriverWait(self.driver, self.timeout)
            logger.info("Chrome WebDriver initialized successfully")

        except Exception as e:
            logger.error(f"Failed to initialize WebDriver: {e}")
            raise

    def close_driver(self):
        """Close the WebDriver"""
        if self.driver:
            self.driver.quit()
            logger.info("WebDriver closed")

    def extract_article_data(self, article_element) -> Optional[Dict[str, str]]:
        """
        Extract data from a single article element

        Args:
            article_element: Selenium WebElement for the article

        Returns:
            Dictionary with article data or None if extraction fails
        """
        try:
            # Extract title and URL
            title_element = article_element.find_element(
                By.CSS_SELECTOR,
                "h1.lxb_af-template_tags-get_linked_post_title a"
            )
            title = title_element.text.strip()
            article_url = title_element.get_attribute("href")

            # Extract date
            date_element = article_element.find_element(
                By.CSS_SELECTOR,
                "time.lxb_af-template_tags-get_post_date"
            )
            date_text = date_element.text.strip()
            datetime_attr = date_element.get_attribute("datetime")

            # Extract content preview
            content_element = article_element.find_element(
                By.CSS_SELECTOR,
                "div.lxb_af-post_content"
            )

            # Get all paragraph text, excluding the "Continue Reading" link
            paragraphs = content_element.find_elements(By.TAG_NAME, "p")
            content_parts = []
            for p in paragraphs:
                text = p.text.strip()
                if text and not text.startswith("Continue Reading"):
                    content_parts.append(text)

            content = " ".join(content_parts)

            # Extract author if available
            try:
                author_element = article_element.find_element(
                    By.CSS_SELECTOR,
                    ".lxb_af-template_tags-get_authors a"
                )
                author = author_element.text.strip()
            except NoSuchElementException:
                author = "Unknown"

            article_data = {
                "title": title,
                "url": article_url,
                "date": date_text,
                "datetime": datetime_attr,
                "author": author,
                "content_preview": content,
                "scraped_at": datetime.now().isoformat()
            }

            logger.info(f"Extracted article: {title}")
            return article_data

        except Exception as e:
            logger.error(f"Failed to extract article data: {e}")
            return None

    def get_full_article_content(self, article_url: str) -> Optional[str]:
        """
        Navigate to individual article page and extract full content

        Args:
            article_url: URL of the article

        Returns:
            Full article content or None if extraction fails
        """
        try:
            logger.info(f"Fetching full content from: {article_url}")
            self.driver.get(article_url)

            # Wait for article content to load
            content_element = self.wait.until(
                EC.presence_of_element_located((By.CSS_SELECTOR, ".entry-content, .post-content, .article-content"))
            )

            # Extract all paragraphs from the article content
            paragraphs = content_element.find_elements(By.TAG_NAME, "p")
            content_parts = []

            for p in paragraphs:
                text = p.text.strip()
                if text:
                    content_parts.append(text)

            full_content = "\n\n".join(content_parts)
            logger.info(f"Successfully extracted full content ({len(full_content)} characters)")
            return full_content

        except Exception as e:
            logger.error(f"Failed to extract full article content from {article_url}: {e}")
            return None

    def scrape_articles(self, max_articles: int = 20, include_full_content: bool = False) -> List[Dict[str, str]]:
        """
        Scrape articles from the food recalls page

        Args:
            max_articles: Maximum number of articles to scrape
            include_full_content: Whether to fetch full article content (slower)

        Returns:
            List of article dictionaries
        """
        articles = []

        try:
            logger.info(f"Starting to scrape articles from: {self.base_url}")
            self.driver.get(self.base_url)

            # Wait for articles to load
            self.wait.until(
                EC.presence_of_element_located((By.CSS_SELECTOR, "article.post"))
            )

            # Find all article elements
            article_elements = self.driver.find_elements(
                By.CSS_SELECTOR,
                "article.post[data-qa='exclude']"
            )

            logger.info(f"Found {len(article_elements)} articles on the page")

            # Process each article
            for i, article_element in enumerate(article_elements[:max_articles]):
                logger.info(f"Processing article {i+1}/{min(len(article_elements), max_articles)}")

                article_data = self.extract_article_data(article_element)
                if article_data:
                    # Optionally fetch full content
                    if include_full_content:
                        full_content = self.get_full_article_content(article_data["url"])
                        if full_content:
                            article_data["full_content"] = full_content

                        # Navigate back to the main page
                        self.driver.get(self.base_url)
                        time.sleep(2)  # Brief pause to avoid overwhelming the server

                    articles.append(article_data)

                # Brief pause between articles
                time.sleep(1)

            logger.info(f"Successfully scraped {len(articles)} articles")
            return articles

        except Exception as e:
            logger.error(f"Failed to scrape articles: {e}")
            return articles

    def save_to_json(self, articles: List[Dict[str, str]], filename: str = None):
        """Save articles to JSON file"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"food_safety_news_{timestamp}.json"

        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(articles, f, indent=2, ensure_ascii=False)
            logger.info(f"Saved {len(articles)} articles to {filename}")
        except Exception as e:
            logger.error(f"Failed to save JSON file: {e}")

    def save_to_csv(self, articles: List[Dict[str, str]], filename: str = None):
        """Save articles to CSV file"""
        if not articles:
            logger.warning("No articles to save")
            return

        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"food_safety_news_{timestamp}.csv"

        try:
            with open(filename, 'w', newline='', encoding='utf-8') as f:
                writer = csv.DictWriter(f, fieldnames=articles[0].keys())
                writer.writeheader()
                writer.writerows(articles)
            logger.info(f"Saved {len(articles)} articles to {filename}")
        except Exception as e:
            logger.error(f"Failed to save CSV file: {e}")


def main():
    """Main function to run the scraper"""
    scraper = FoodSafetyNewsScraper(headless=True, timeout=15)

    try:
        # Setup the driver
        scraper.setup_driver()

        # Scrape articles (adjust parameters as needed)
        articles = scraper.scrape_articles(
            max_articles=10,  # Scrape first 10 articles
            include_full_content=True  # Set to False for faster scraping
        )

        if articles:
            # Save results
            scraper.save_to_json(articles)
            scraper.save_to_csv(articles)

            # Print summary
            print(f"\n=== Scraping Summary ===")
            print(f"Total articles scraped: {len(articles)}")
            print(f"Articles with full content: {sum(1 for a in articles if 'full_content' in a)}")

            # Print first article as example
            if articles:
                print(f"\n=== First Article Example ===")
                first_article = articles[0]
                print(f"Title: {first_article['title']}")
                print(f"Date: {first_article['date']}")
                print(f"Author: {first_article['author']}")
                print(f"URL: {first_article['url']}")
                print(f"Content Preview: {first_article['content_preview'][:200]}...")
        else:
            print("No articles were scraped successfully")

    except Exception as e:
        logger.error(f"Scraping failed: {e}")
    finally:
        scraper.close_driver()


if __name__ == "__main__":
    main()